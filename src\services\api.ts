// import { AuditLogFilters } from '@/hooks/useAuditLogs';

import { FilterTypes } from "@/types/audit";

// Constants
const BASE_URL = 'https://devapi-auditlog.clinicaltrac.net';

const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  AUTH_CODE: 'authorization_code',
  STATE: 'state',
  SCHOOL_ID: 'school_id'
};

interface TokenResponse {
  data: {
    accessToken: string;
    refreshToken: string;
  };
}

interface QueryParams {
  [key: string]: string;
}

// Utility to parse query parameters from the URL
export function getQueryParams(): QueryParams {
  const params = new URLSearchParams(window.location.search);
  const queryParams: QueryParams = {};

  // Parse query params from URL and store them
  if (params.toString()) {
    params.forEach((value, key) => {
      queryParams[key] = value;
      sessionStorage.setItem(key, value);
    });
    // Clear the URL after storing params
    window.history.replaceState({}, document.title, window.location.pathname);
  }

  // Get from sessionStorage as fallback
  [STORAGE_KEYS.AUTH_CODE, STORAGE_KEYS.STATE, STORAGE_KEYS.SCHOOL_ID].forEach((key) => {
    if (!queryParams[key]) {
      const value = sessionStorage.getItem(key);
      if (value) {
        queryParams[key] = value;
      }
    }
  });

  return queryParams;
}

// Token management functions
export function storeTokens(accessToken: string, refreshToken: string): void {
  try {
    if (!accessToken || !refreshToken) {
      throw new Error('Invalid tokens provided to storeTokens');
    }

    sessionStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
    sessionStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);

    // Verify storage
    const storedAccess = sessionStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    const storedRefresh = sessionStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);

    if (!storedAccess || !storedRefresh) {
      throw new Error('Failed to verify token storage');
    }
  } catch (error) {
    console.error('Error storing tokens:', error);
    throw error;
  }
}

export function clearTokens(): void {
  sessionStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
  sessionStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
}

export function getStoredTokens(): { accessToken: string | null; refreshToken: string | null } {
  return {
    accessToken: sessionStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN),
    refreshToken: sessionStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  };
}

// API functions with proper error handling and logging
export async function fetchTokens(authorizationCode: string, state: string, schoolId: string): Promise<TokenResponse['data']> {
  const url = `${BASE_URL}/oauth/token?authorization_code=${authorizationCode}&state=${state}&school_id=${schoolId}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: TokenResponse = await response.json();

    if (!data?.data?.accessToken || !data?.data?.refreshToken) {
      throw new Error('Invalid token data received from server');
    }

    storeTokens(data.data.accessToken, data.data.refreshToken);
    return data.data;

  } catch (error) {
    console.error('Error fetching tokens:', error);
    throw error;
  }
}

// Optimized fetch logs function with better error handling
// Updated fetchAuditLogsWithAuth function with username and metadata support


// Add these new filter types to your FilterTypes interface
export interface ExtendedFilterTypes extends FilterTypes {
  userName?: string;
  metadata?: string;
}

// Optimized fetch logs function with better error handling and new filters
export async function fetchAuditLogsWithAuth(
  page: number,
  rowsPerPage: number,
  filters: ExtendedFilterTypes = {}
) {
  const { accessToken } = getStoredTokens();
  const queryParams = getQueryParams();
  const {school_id, state } = queryParams;

  if (!accessToken) {
    throw new Error('No access token available');
  }

  try {
    // Calculate skip for pagination
    const skip = (page - 1) * rowsPerPage;
    const baseFilters = school_id === '0' ? {} : { schoolId: school_id };
    // Determine serviceKey based on state from the URL query parameters
    let serviceKey;
    if (state === 'CT-RT') {
      serviceKey = 1;
    } else if (state === 'CT-RAD') {
      serviceKey = 2;
    } else {
      throw new Error("Invalid state parameter, cannot determine serviceKey.");
    }

    // Create API-friendly filters format
    const formattedFilters: Record<string, any> = {...baseFilters,};
    
    // Map filters to API format
    if (filters.schoolId && filters.schoolId !== 'all') {
      formattedFilters.schoolId = filters.schoolId;
    }
    
    if (filters.resource && filters.resource !== 'all') {
      formattedFilters.resource = filters.resource;
    }
    
    if (filters.action && filters.action !== 'all') {
      formattedFilters.action = filters.action;
    }
    
    if (filters.search) {
      formattedFilters.search = filters.search;  
    }
    
    // Add new username filter
    if (filters.userName) {
      formattedFilters.userName = filters.userName;
    }
    
    // Add new metadata filter
    if (filters.metadata) {
      formattedFilters.metadata = filters.metadata;
    }
    
    // Format date filters
    if (filters.fromDate) {
      formattedFilters.fromDate = new Date(filters.fromDate).toLocaleDateString('en-CA') + 'T00:00:00.000Z';
    }
    
    if (filters.toDate) {
      formattedFilters.toDate = new Date(filters.toDate).toLocaleDateString('en-CA') + 'T23:59:59.999Z';
    }
    
    // Handle device type
    if (filters.isMobile !== undefined && filters.isMobile !== 'all') {
      formattedFilters.isMobile = filters.isMobile;
    }

    // Handle user filter
    if (filters.userId && filters.userId !== 'all') {
      formattedFilters.userId = filters.userId;
    }

    // Build API URL - exactly matching your provided URL structure
    const apiUrl = `${BASE_URL}/logs/dynamic-search?filters=${encodeURIComponent(
      JSON.stringify(formattedFilters)
    )}&skip=${skip}&limit=${rowsPerPage}&serviceKey=${serviceKey}`;

    console.log('API Request URL:', apiUrl);
    console.log('Formatted Filters:', formattedFilters);

    const response = await fetch(apiUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (response.status === 401) {
      throw { response: { status: 401 } };
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data?.data;
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    throw error;
  }
}

export function checkSessionStatus() {
  const { accessToken, refreshToken } = getStoredTokens();
  return {
    isValid: !!(accessToken && refreshToken),
    tokens: { accessToken, refreshToken }
  };
}

export function redirectToDashboard(): void {
  const queryParams = getQueryParams();
  const { state } = queryParams;
  
  let dashboardPath = '/dashboard';
  
  // Set path based on state
  if (state === 'CT-RT') {
    dashboardPath = '/rt/dashboard';
  } else if (state === 'CT-RAD') {
    dashboardPath = '/rad/dashboard';
  }
  
  const currentPath = window.location.pathname;
  if (currentPath !== dashboardPath) {
    window.location.href = dashboardPath;
  }
}

// Session expired modal functions
export function createSessionExpiredModal() {
  // Check if modal already exists
  const existingModal = document.getElementById('session-expired-modal');
  if (existingModal) return existingModal;
  
  const modal = document.createElement('div');
  modal.id = 'session-expired-modal';
  modal.style.display = 'none';
  modal.style.position = 'fixed';
  modal.style.top = '0';
  modal.style.left = '0';
  modal.style.width = '100%';
  modal.style.height = '100%';
  modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
  modal.style.zIndex = '1000';

  const modalContent = document.createElement('div');
  modalContent.style.backgroundColor = 'white';
  modalContent.style.margin = '15% auto';
  modalContent.style.padding = '20px';
  modalContent.style.width = '50%';
  modalContent.style.borderRadius = '5px';
  modalContent.style.textAlign = 'center';

  const title = document.createElement('h2');
  title.textContent = 'Session Expired';

  const message = document.createElement('p');
  message.textContent = 'Your session has expired. Please login again.';

  const button = document.createElement('button');
  button.textContent = 'Login';
  button.style.padding = '8px 16px';
  button.style.backgroundColor = '#4CAF50';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  button.style.marginTop = '15px';

  button.addEventListener('click', () => {
    window.close(); // Close window to force re-login
  });

  modalContent.appendChild(title);
  modalContent.appendChild(message);
  modalContent.appendChild(button);
  modal.appendChild(modalContent);

  document.body.appendChild(modal);
  return modal;
}

export function showSessionExpiredModal(modal: HTMLElement) {
  modal.style.display = 'block';
}

// Authentication handler function
export async function handleAuthentication() {
  try {
    const params = new URLSearchParams(window.location.search);
    const hasAuthParams =
      params.has(STORAGE_KEYS.AUTH_CODE) &&
      params.has(STORAGE_KEYS.STATE) &&
      params.has(STORAGE_KEYS.SCHOOL_ID);

    // Process auth params if present
    if (hasAuthParams) {
      clearTokens();
      const queryParams = getQueryParams();
      const { authorization_code, state, school_id } = queryParams;

      try {
        const tokens = await fetchTokens(authorization_code, state, school_id);

        if (!tokens?.accessToken) {
          throw new Error('Failed to obtain new tokens');
        }

        // Successfully obtained tokens - redirect to dashboard
        redirectToDashboard();
        
        return { success: true, newTokensObtained: true };
      } catch (error) {
        console.error('Error fetching tokens:', error);
        return { success: false, reason: 'token_fetch_failed', error };
      }
    }

    // Check session status if no auth params
    const sessionStatus = checkSessionStatus();
    if (!sessionStatus.isValid) {
      return { success: false, reason: 'session_expired' };
    }

    return { success: true, newTokensObtained: false };
  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, reason: 'unknown_error', error };
  }
}

// Self-executing initialization function
(function initialize() {
  handleAuthentication()
    .then(result => {
      if (!result.success) {
        if (result.reason === 'session_expired') {
          // Show session expired modal
          const sessionModal = document.getElementById('session-expired-modal') ||
                             createSessionExpiredModal();
          showSessionExpiredModal(sessionModal);
        }
        else if (result.reason === 'token_fetch_failed') {
          console.error('Failed to fetch tokens, check authorization parameters');
          // Show appropriate error message to user
        }
      }
    })
    .catch(error => {
      console.error('Initialization error:', error);
    });
})();
