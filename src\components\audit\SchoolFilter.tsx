import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '../ui/input';
import { Cross1Icon } from '@radix-ui/react-icons';

interface SchoolFilterProps {
  schools: Array<{ schoolId: string; schoolName: string }>;
  selectedSchool: string;
  onSchoolChange: (value: string) => void;
}

export function SchoolFilter({
  schools,
  selectedSchool,
  onSchoolChange,
}: SchoolFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  const handleValueChange = (value: string) => {
    onSchoolChange(value);
    setIsOpen(false);
  };
  useEffect(() => {
      const timer = setTimeout(() => setDebouncedSearchTerm(searchTerm), 300);
      return () => clearTimeout(timer);
    }, [searchTerm]);
    const sortedSchools = [...schools].sort((a, b) =>
      a.schoolName.localeCompare(b.schoolName)
    );
    
    const filteredSchools = sortedSchools.filter((school) =>
      school.schoolName.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
    
  const handleClearFilter = () => {
    localStorage.removeItem('selectedSchool');
    onSchoolChange('all');
  };



  const handleSearchClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(true);
  };
  const handleDropdownToggle = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
    }
  };
  // Sort schools alphabetically
  // const sortedSchools = [...schools].sort((a, b) =>
  //   a.schoolName.localeCompare(b.schoolName)
  // );

  return (
    <div className="space-y-1">
      <div className="flex items-center">
        <h3 className="text-md font-medium">School Name</h3>
        {selectedSchool !== 'all' && (
          <button
            className="p-1.5 ml-4 rounded-full bg-slate-50 hover:bg-gray-100"
            onClick={handleClearFilter}
          >
            <Cross1Icon className="h-4 w-4 text-gray-800" />
          </button>
        )}
      </div>
      <div className="flex items-center gap-2">
        <Select value={selectedSchool} onValueChange={handleValueChange} open={isOpen}      onOpenChange={handleDropdownToggle}  >
          <SelectTrigger className="truncate max-w-[270px]">
            <SelectValue placeholder="Select School" />
          </SelectTrigger>
          <SelectContent>
            <div className="px-2 py-1 sticky top-0 bg-white z-10">
             <Input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => {
                              e.preventDefault();
                              setSearchTerm(e.target.value);
                            }}
                            placeholder="Search school..."
                            className="mb-2"
                            onClick={handleSearchClick}
                            onKeyDown={(e) => e.stopPropagation()}
                            autoComplete="off"
                          />
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              <SelectItem value="all">All Schools</SelectItem>
              {filteredSchools.map((school) => (
                <SelectItem key={school.schoolId} value={school.schoolId}>
                  {school.schoolName}
                </SelectItem>
              ))}
            </div>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
