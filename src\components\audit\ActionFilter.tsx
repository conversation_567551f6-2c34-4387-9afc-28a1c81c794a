import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Cross1Icon } from '@radix-ui/react-icons';
import React from 'react';

interface ActionFilterProps {
  actions: string[];
  selectedAction: string;
  onActionChange: (value: string) => void;
}

export function ActionFilter({
  actions,
  selectedAction,
  onActionChange,
}: ActionFilterProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [isOpen, setIsOpen] = useState(false);

  // Debouncing logic
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedSearchTerm(searchTerm), 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Sort resources alphabetically before filtering
  const sortedActions = [...actions].sort((a, b) => a.localeCompare(b));

  // Filter resources based on search term
  const filteredActions = sortedActions.filter((actions) =>
    actions.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
  );

  const handleSearchClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(true);
  };

  const handleValueChange = (value: string) => {
    onActionChange(value);
    setIsOpen(false);
    setSearchTerm('');
    setDebouncedSearchTerm('');
  };

  const handleDropdownToggle = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
    }
  };

  const handleClear = () => {
    onActionChange('all');
  };

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Action</h3>
      <div className="flex items-center gap-2">
        <Select 
          value={selectedAction} 
          onValueChange={handleValueChange}
          open={isOpen}
          onOpenChange={handleDropdownToggle}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select action" />
          </SelectTrigger>
          <SelectContent>
            <div className="px-2 py-1 sticky top-0 bg-white z-10">
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search modules..."
                className="mb-2"
                onClick={handleSearchClick}
                onKeyDown={(e) => e.stopPropagation()}
                autoComplete="off"
              />
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              <SelectItem value="all">All Actions</SelectItem>
              {filteredActions.map((action) => (
                <SelectItem key={action} value={action}>
                  {action}
                </SelectItem>
              ))}
            </div>
          </SelectContent>
        </Select>
        {selectedAction !== 'all' && (
          <button
            className="p-1.5 rounded-full bg-slate-50 hover:bg-gray-100"
            onClick={handleClear}
          >
            <Cross1Icon className="h-4 w-4 text-gray-500" />
          </button>
        )}
      </div>
    </div>
  );
}
