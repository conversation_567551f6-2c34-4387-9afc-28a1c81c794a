// import React, { createContext, useContext, useState, useEffect } from 'react';
// import { fetchAuditLogs } from '../services/api';

// interface AuthContextType {
//   accessToken: string | null;
//   login: (username: string, password: string) => Promise<void>;
//   logout: () => void;
//   isAuthenticated: boolean;
// }

// const AuthContext = createContext<AuthContextType | undefined>(undefined);

// export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
//   const [accessToken, setAccessToken] = useState<string | null>(null);

//   const login = async (username: string, password: string) => {
//     const response = await auditLogService.login({ username, password });
//     setAccessToken(response.accessToken);
//   };

//   const logout = () => {
//     setAccessToken(null);
//     // Add any additional logout logic
//   };

//   useEffect(() => {
//     // Optionally, handle token refresh logic here
//   }, []);

//   return (
//     <AuthContext.Provider value={{ accessToken, login, logout, isAuthenticated: !!accessToken }}>
//       {children}
//     </AuthContext.Provider>
//   );
// };

// export const useAuth = () => {
//   const context = useContext(AuthContext);
//   if (!context) throw new Error('useAuth must be used within an AuthProvider');
//   return context;
// };
