import { startOfToday, endOfToday, startOfYesterday, endOfYesterday, subDays } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { DateRangeType } from '@/types/audit';

export function getDateRangeFromType(type: DateRangeType): DateRange {
  switch (type) {
    case 'today':
      return {
        from: startOfToday(),
        to: endOfToday(),
      };
    case 'yesterday':
      return {
        from: startOfYesterday(),
        to: endOfYesterday(),
      };
    case 'last7days':
      return {
        from: subDays(startOfToday(), 6),
        to: endOfToday(),
      };
    default:
      return {
        from: undefined,
        to: undefined,
      };
  }
}

export function isValidDate(date: Date): boolean {
  return date instanceof Date && !isNaN(date.getTime());
}