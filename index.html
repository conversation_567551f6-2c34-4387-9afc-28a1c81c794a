<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Audit Log</title>
    
    <!-- Default icon -->
    <link id="dynamic-favicon" rel="icon" type="image/x-icon" href="/favicon.ico" />
    
    <script>
      // This script runs before your app starts
      const setFavicon = () => {
        const path = window.location.pathname;
        const favicon = document.getElementById('dynamic-favicon');
        
        if (path.startsWith('/rad')) {
          favicon.href = '/radfavicon.ico';
        } else {
          favicon.href = '/favicon.ico';
        }
      };

      setFavicon();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
