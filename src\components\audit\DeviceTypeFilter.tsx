import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Cross1Icon } from '@radix-ui/react-icons';

interface DeviceTypeFilterProps {
  selectedDeviceType: string;
  onDeviceTypeChange: (deviceType: string) => void;
}

export function DeviceTypeFilter({ selectedDeviceType, onDeviceTypeChange }: DeviceTypeFilterProps) {
  const handleDeviceSelection = (value: string) => {
    const finalValue = value === "all" ? "" : value;
    setTimeout(() => {
      onDeviceTypeChange(finalValue);
    }, 0);
  };

  const handleRemoveFilter = () => {
    onDeviceTypeChange("");
  };

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Device Type</h3>
      <div className="flex items-center space-x-2">
        <Select value={selectedDeviceType || "all"} onValueChange={handleDeviceSelection}>
          <SelectTrigger className="bg-white">
            <SelectValue placeholder="Select device type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Devices</SelectItem>
            <SelectItem value="1">Mobile</SelectItem>
            <SelectItem value="0">Web</SelectItem>
          </SelectContent>
        </Select>
        {selectedDeviceType !== "" && selectedDeviceType !== "all" && (
  <button className="p-1.5 ml-4 rounded-full bg-slate-50 hover:bg-gray-100" onClick={handleRemoveFilter}>
    <Cross1Icon className="h-4 w-4 text-gray-800" /> 
  </button>
)}

      </div>
    </div>
  );
}